import { Header } from "@/components/layout/header"
import { SignUpForm } from "@/components/auth/sign-up-form"
import { Testimonial } from "@/components/layout/testimonial"

export default function SignUp() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="flex min-h-[calc(100vh-3.5rem)]">
        {/* Left side - Sign up form */}
        <div className="flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:flex-none lg:px-20 xl:px-24">
          <div className="mx-auto w-full max-w-sm lg:w-96">
            <SignUpForm />
          </div>
        </div>

        {/* Right side - Testimonial */}
        <div className="relative hidden w-0 flex-1 lg:block">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
            <div className="absolute inset-0 bg-gradient-to-br from-green-900/20 via-transparent to-blue-900/20" />
            <Testimonial />
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t border-gray-800 bg-background/95 backdrop-blur">
        <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
          <p className="text-center text-xs text-gray-400">
            By continuing, you agree to Supabase&apos;s{" "}
            <a href="/terms" className="text-green-400 hover:text-green-300 transition-colors">
              Terms of Service
            </a>{" "}
            and{" "}
            <a href="/privacy" className="text-green-400 hover:text-green-300 transition-colors">
              Privacy Policy
            </a>
            , and to receive periodic emails with updates.
          </p>
        </div>
      </footer>
    </div>
  );
}
